.pio/build/esp32dev/libb3b/BLE/BLESecurity.cpp.o: \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/BLE/src/BLESecurity.cpp \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/BLE/src/BLESecurity.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/dio_qspi/include/sdkconfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/bt/host/bluedroid/api/include/api/esp_gap_ble_api.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_compiler.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/bt/host/bluedroid/api/include/api/esp_bt_defs.h
