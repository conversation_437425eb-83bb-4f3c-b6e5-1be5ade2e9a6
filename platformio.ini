[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200

lib_deps =
    olikraus/U8g2@^2.35.19
    h2zero/NimBLE-Arduino@^1.4.2
    T-vK/ESP32 BLE Keyboard@^0.3.2
    https://github.com/me-no-dev/ESPAsyncWebServer.git
    https://github.com/me-no-dev/AsyncTCP.git
    bblanchon/Arduino<PERSON>son@^7.2.0

build_flags =
    -D FW_VERSION=\"1.0.0\"
    -D PROJECT_NAME=\"ESP-Ducky\"
    -D USE_SPIFFS
    -D CORE_DEBUG_LEVEL=0
    -D CONFIG_BT_NIMBLE_ROLE_PERIPHERAL=1
    -D CONFIG_BT_NIMBLE_ROLE_BROADCASTER=1
    -Os
    -ffunction-sections
    -fdata-sections
    -Wl,--gc-sections

board_build.partitions = partitions.csv
upload_port = COM4
monitor_port = COM4

; SPIFFS configuration
board_build.filesystem = spiffs
