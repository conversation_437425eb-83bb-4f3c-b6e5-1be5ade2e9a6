.pio/build/esp32dev/libeb0/NimBLE-Arduino/NimBLEUUID.cpp.o: \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/NimBLEUUID.cpp \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimconfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/dio_qspi/include/sdkconfig.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimconfig_rename.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/NimBLEUtils.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_gap.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/include/nimble/hci_common.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/include/nimble/ble.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/syscfg/syscfg.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/esp_port/port/include/esp_nimble_cfg.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/../syscfg/syscfg.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/include/nimble/nimble_npl.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/FreeRTOS.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_compiler.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/FreeRTOSConfig_arch.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/xtensa_config.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/hal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa-versions.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core-isa.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/core-matmap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/tie.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/system.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/corebits.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime-frames.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp_rom_sys.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/reset_reasons.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_rom/include/esp32/rom/ets_sys.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/projdefs.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/portable.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/deprecated_definitions.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portmacro.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/specreg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime-core-state.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xt_instr_macros.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtruntime.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/spinlock.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/cpu.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_cpu.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/cpu_hal.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_err.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_compiler.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/soc_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/include/hal/cpu_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/hal/esp32/include/hal/cpu_ll.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_attr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/esp32/include/xtensa/config/extreg.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_bit_defs.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/soc/compare_set.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/include/soc/soc_memory_types.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/soc/esp32/include/soc/soc.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_assert.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_system/include/esp_private/crosscore_int.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_timer/include/esp_timer.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/newlib/platform_include/esp_newlib.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/heap/include/esp_heap_caps.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/heap/include/multi_heap.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_system/include/esp_system.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_common/include/esp_idf_version.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_mac.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_chip_info.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/esp_hw_support/include/esp_random.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portbenchmark.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_api.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/xtensa/include/xtensa/xtensa_context.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/port/xtensa/include/freertos/portmacro_deprecated.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/mpu_wrappers.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/queue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/task.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/list.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/esp_additions/freertos/task_snapshot.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/task.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/semphr.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/queue.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/freertos/include/freertos/timers.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/npl/freertos/include/nimble/npl_freertos.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/endian.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/queue.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os_error.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os_mbuf.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/os_mempool.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_att.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/os/queue.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_eddystone.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_gap.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_gatt.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_uuid.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_adv.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_id.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/include/nimble/ble.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_hci.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_log.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/modlog/modlog.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/modlog/../log_common/log_common.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/modlog/../log_common/ignore.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/modlog/../log/log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log.h \
 C:/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32/include/log/include/esp_log_internal.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/log/log.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/logcfg/logcfg.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/logcfg/../modlog/modlog.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/logcfg/../log_common/log_common.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_mbuf.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_hs_stop.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_ibeacon.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_l2cap.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/include/nimble/nimble_opt.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/include/nimble/nimble_opt_auto.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_sm.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_store.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/NimBLEUUID.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/nimble/host/include/host/ble_uuid.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/NimBLELog.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/porting/nimble/include/syscfg/syscfg.h \
 .pio/libdeps/esp32dev/NimBLE-Arduino/src/nimble/console/console.h
